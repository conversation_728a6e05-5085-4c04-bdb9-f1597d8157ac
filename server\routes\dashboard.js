const express = require('express');
const router = express.Router();
const pool = require('../config/database');
const { getUserRole } = require('../utils/userRoleHelper');

// Get dashboard summary
router.get('/summary', async (req, res) => {
  try {
    const { user_id, branch_id, date_filter = 'today', user_type } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Get user role to determine access level
    const userRole = await getUserRole(user_id, user_type);
    if (!userRole) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Build date filter conditions
    let dateCondition = '';
    const now = new Date();

    switch (date_filter) {
      case 'today':
        const today = now.toISOString().split('T')[0];
        dateCondition = `AND DATE(t.date) = '${today}'`;
        break;
      case 'week':
        const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
        dateCondition = `AND t.date >= '${weekStart.toISOString().split('T')[0]}'`;
        break;
      case 'month':
        const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
        dateCondition = `AND t.date >= '${monthStart.toISOString().split('T')[0]}'`;
        break;
      case 'year':
        const yearStart = new Date(now.getFullYear(), 0, 1);
        dateCondition = `AND t.date >= '${yearStart.toISOString().split('T')[0]}'`;
        break;
    }

    // Build branch filter condition
    let branchCondition = '';
    if (branch_id) {
      branchCondition = `AND t.branch_id = ${branch_id}`;
    }

    // Base condition for user access - menangani kasir yang dibuat admin langsung dan melalui cabang
    let userCondition = '';
    if (branch_id) {
      // Jika ada branch_id yang dipilih, filter berdasarkan branch_id
      // Untuk branch user: hanya tampilkan transaksi dari branch mereka
      // Untuk admin: tampilkan transaksi dari branch yang dipilih + transaksi langsung admin
      userCondition = `(
        t.branch_id = ${branch_id} OR
        t.cashier_id IN (SELECT id FROM cashiers WHERE branch_id = ${branch_id}) OR
        (t.outlet_id = ${user_id} AND t.branch_id IS NULL)
      )`;
    } else {
      // Filter berdasarkan role user
      if (userRole === 'admin') {
        // Admin: tampilkan semua transaksi yang terkait dengan admin
        userCondition = `(
          t.outlet_id = ${user_id} OR
          t.branch_id IN (SELECT id FROM branches WHERE user_id = ${user_id}) OR
          t.cashier_id IN (SELECT id FROM cashiers WHERE user_id = ${user_id}) OR
          t.cashier_id IN (SELECT id FROM cashiers WHERE branch_id IN (SELECT id FROM branches WHERE user_id = ${user_id}))
        )`;
      } else if (userRole === 'branch') {
        // Branch user: hanya tampilkan transaksi dari branch mereka
        userCondition = `(
          t.branch_id = ${user_id} OR
          t.cashier_id IN (SELECT id FROM cashiers WHERE branch_id = ${user_id})
        )`;
      } else {
        return res.status(403).json({ error: 'Access denied' });
      }
    }

    // Get total sales and transactions
    const [salesData] = await pool.query(`
      SELECT
        COALESCE(SUM(t.total), 0) as total_sales,
        COUNT(t.id) as total_transactions
      FROM transactions t
      WHERE ${userCondition} ${dateCondition} AND t.payment_status = 'paid'
    `);

    // Get today's sales and transactions
    const todayDate = new Date().toISOString().split('T')[0];
    const [todayData] = await pool.query(`
      SELECT
        COALESCE(SUM(t.total), 0) as today_sales,
        COUNT(t.id) as today_transactions
      FROM transactions t
      WHERE ${userCondition} AND DATE(t.date) = '${todayDate}' AND t.payment_status = 'paid'
    `);

    // Get month's sales and transactions
    const monthStart = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
    const [monthData] = await pool.query(`
      SELECT
        COALESCE(SUM(t.total), 0) as month_sales,
        COUNT(t.id) as month_transactions
      FROM transactions t
      WHERE ${userCondition} AND t.date >= '${monthStart.toISOString().split('T')[0]}' AND t.payment_status = 'paid'
    `);

    // Get total customers (unique customer names)
    const [customerData] = await pool.query(`
      SELECT COUNT(DISTINCT t.customer_name) as total_customers
      FROM transactions t
      WHERE ${userCondition} AND t.payment_status = 'paid'
    `);

    // Get total products
    let productQuery = `
      SELECT COUNT(*) as total_products
      FROM products p
      WHERE p.is_active = 1
    `;

    if (branch_id) {
      // For branch users or admin filtering by branch: show products from that branch or admin's products
      productQuery += ` AND (p.user_id IN (SELECT user_id FROM branches WHERE id = ${branch_id}) OR p.user_id = ${user_id})`;
    } else {
      // Filter berdasarkan role user
      if (userRole === 'admin') {
        // Admin: show all their products and their branches' products
        productQuery += ` AND (p.user_id = ${user_id} OR p.user_id IN (SELECT user_id FROM branches WHERE user_id = ${user_id}))`;
      } else if (userRole === 'branch') {
        // Branch user: only show products from their branch or created by them
        productQuery += ` AND (p.branch_id = ${user_id} OR p.user_id = ${user_id})`;
      }
    }

    const [productData] = await pool.query(productQuery);

    // Get top products
    let topProductsQuery = `
      SELECT
        p.id,
        p.name,
        SUM(ti.quantity) as quantity,
        SUM(ti.quantity * ti.price) as sales
      FROM transaction_items ti
      JOIN transactions t ON ti.transaction_id = t.id
      JOIN products p ON ti.product_id = p.id
      WHERE ${userCondition} ${dateCondition} AND t.payment_status = 'paid'
      GROUP BY p.id, p.name
      ORDER BY sales DESC
      LIMIT 10
    `;

    const [topProducts] = await pool.query(topProductsQuery);

    // Get recent transactions
    let recentTransactionsQuery = `
      SELECT
        t.id,
        t.customer_name,
        t.total,
        t.created_at as date,
        t.payment_method,
        t.payment_status
      FROM transactions t
      WHERE ${userCondition}
      ORDER BY t.created_at DESC
      LIMIT 10
    `;

    const [recentTransactions] = await pool.query(recentTransactionsQuery);

    // Prepare response data
    const summary = {
      totalSales: salesData[0].total_sales || 0,
      totalTransactions: salesData[0].total_transactions || 0,
      totalCustomers: customerData[0].total_customers || 0,
      totalProducts: productData[0].total_products || 0,
      todaySales: todayData[0].today_sales || 0,
      todayTransactions: todayData[0].today_transactions || 0,
      monthSales: monthData[0].month_sales || 0,
      monthTransactions: monthData[0].month_transactions || 0,
      topProducts: topProducts || [],
      recentTransactions: recentTransactions || []
    };

    res.json(summary);
  } catch (error) {
    console.error('Error fetching dashboard summary:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard summary' });
  }
});

// Get sales chart data
router.get('/sales-chart', async (req, res) => {
  try {
    const { user_id, branch_id, period = 'week' } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    let dateFormat = '';
    let dateCondition = '';
    const now = new Date();

    switch (period) {
      case 'week':
        dateFormat = 'DATE(created_at)';
        const weekStart = new Date(now.setDate(now.getDate() - 7));
        dateCondition = `AND created_at >= '${weekStart.toISOString().split('T')[0]}'`;
        break;
      case 'month':
        dateFormat = 'DATE(created_at)';
        const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
        dateCondition = `AND created_at >= '${monthStart.toISOString().split('T')[0]}'`;
        break;
      case 'year':
        dateFormat = 'DATE_FORMAT(created_at, "%Y-%m")';
        const yearStart = new Date(now.getFullYear(), 0, 1);
        dateCondition = `AND created_at >= '${yearStart.toISOString().split('T')[0]}'`;
        break;
    }

    // Build user condition for chart data - sama seperti summary
    let userCondition = '';
    if (branch_id) {
      // Jika ada branch_id yang dipilih, filter berdasarkan branch_id
      // Untuk branch user: hanya tampilkan transaksi dari branch mereka
      // Untuk admin: tampilkan transaksi dari branch yang dipilih + transaksi langsung admin
      userCondition = `(
        branch_id = ${branch_id} OR
        cashier_id IN (SELECT id FROM cashiers WHERE branch_id = ${branch_id}) OR
        (outlet_id = ${user_id} AND branch_id IS NULL)
      )`;
    } else {
      // Jika tidak ada branch_id, tampilkan semua transaksi yang terkait dengan admin:
      // 1. Transaksi langsung dari admin (outlet_id)
      // 2. Transaksi dari cabang milik admin (branch_id)
      // 3. Transaksi dari kasir yang dibuat admin langsung (cashier_id dengan user_id = admin)
      // 4. Transaksi dari kasir yang dibuat melalui cabang (cashier_id dengan branch_id milik admin)
      userCondition = `(
        outlet_id = ${user_id} OR
        branch_id IN (SELECT id FROM branches WHERE user_id = ${user_id}) OR
        cashier_id IN (SELECT id FROM cashiers WHERE user_id = ${user_id}) OR
        cashier_id IN (SELECT id FROM cashiers WHERE branch_id IN (SELECT id FROM branches WHERE user_id = ${user_id}))
      )`;
    }

    const query = `
      SELECT
        ${dateFormat} as date,
        SUM(total) as sales,
        COUNT(*) as transactions
      FROM transactions
      WHERE ${userCondition} ${dateCondition} AND payment_status = 'paid'
      GROUP BY ${dateFormat}
      ORDER BY date ASC
    `;

    const [chartData] = await pool.query(query);

    res.json(chartData);
  } catch (error) {
    console.error('Error fetching sales chart data:', error);
    res.status(500).json({ error: 'Failed to fetch sales chart data' });
  }
});

// Get transaction time distribution (3-hour slots)
router.get('/transaction-time-chart', async (req, res) => {
  try {
    const { user_id, branch_id, date_filter = 'today', user_type } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'user_id is required' });
    }

    // Get user role to determine access level
    const userRole = await getUserRole(user_id, user_type);
    if (!userRole) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Build date condition
    let dateCondition = '';
    const today = new Date();

    switch (date_filter) {
      case 'today':
        const todayStr = today.toISOString().split('T')[0];
        dateCondition = `AND DATE(date) = '${todayStr}'`;
        break;
      case 'week':
        const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        dateCondition = `AND date >= '${weekAgo.toISOString().split('T')[0]}'`;
        break;
      case 'month':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        dateCondition = `AND date >= '${monthStart.toISOString().split('T')[0]}'`;
        break;
      default:
        dateCondition = '';
    }

    // Build user condition for access control
    let userCondition = '';
    if (branch_id) {
      userCondition = `(
        branch_id = ${branch_id} OR
        cashier_id IN (SELECT id FROM cashiers WHERE branch_id = ${branch_id}) OR
        (outlet_id = ${user_id} AND branch_id IS NULL)
      )`;
    } else {
      if (userRole === 'admin') {
        userCondition = `(
          outlet_id = ${user_id} OR
          branch_id IN (SELECT id FROM branches WHERE user_id = ${user_id}) OR
          cashier_id IN (SELECT id FROM cashiers WHERE user_id = ${user_id}) OR
          cashier_id IN (SELECT id FROM cashiers WHERE branch_id IN (SELECT id FROM branches WHERE user_id = ${user_id}))
        )`;
      } else if (userRole === 'branch') {
        userCondition = `(
          branch_id = ${user_id} OR
          cashier_id IN (SELECT id FROM cashiers WHERE branch_id = ${user_id})
        )`;
      } else {
        return res.status(403).json({ error: 'Access denied' });
      }
    }

    const query = `
      SELECT
        CASE
          WHEN HOUR(date) >= 0 AND HOUR(date) < 3 THEN '00:00-03:00'
          WHEN HOUR(date) >= 3 AND HOUR(date) < 6 THEN '03:00-06:00'
          WHEN HOUR(date) >= 6 AND HOUR(date) < 9 THEN '06:00-09:00'
          WHEN HOUR(date) >= 9 AND HOUR(date) < 12 THEN '09:00-12:00'
          WHEN HOUR(date) >= 12 AND HOUR(date) < 15 THEN '12:00-15:00'
          WHEN HOUR(date) >= 15 AND HOUR(date) < 18 THEN '15:00-18:00'
          WHEN HOUR(date) >= 18 AND HOUR(date) < 21 THEN '18:00-21:00'
          ELSE '21:00-24:00'
        END as time_slot,
        COUNT(*) as transaction_count,
        SUM(total) as total_sales
      FROM transactions
      WHERE ${userCondition} ${dateCondition} AND payment_status = 'paid'
      GROUP BY time_slot
      ORDER BY
        CASE
          WHEN HOUR(date) >= 0 AND HOUR(date) < 3 THEN 1
          WHEN HOUR(date) >= 3 AND HOUR(date) < 6 THEN 2
          WHEN HOUR(date) >= 6 AND HOUR(date) < 9 THEN 3
          WHEN HOUR(date) >= 9 AND HOUR(date) < 12 THEN 4
          WHEN HOUR(date) >= 12 AND HOUR(date) < 15 THEN 5
          WHEN HOUR(date) >= 15 AND HOUR(date) < 18 THEN 6
          WHEN HOUR(date) >= 18 AND HOUR(date) < 21 THEN 7
          ELSE 8
        END
    `;

    const [results] = await pool.query(query);

    // Ensure all time slots are represented
    const timeSlots = [
      '00:00-03:00', '03:00-06:00', '06:00-09:00', '09:00-12:00',
      '12:00-15:00', '15:00-18:00', '18:00-21:00', '21:00-24:00'
    ];

    const chartData = timeSlots.map(slot => {
      const found = results.find(r => r.time_slot === slot);
      return {
        time_slot: slot,
        transaction_count: found ? found.transaction_count : 0,
        total_sales: found ? parseFloat(found.total_sales) : 0
      };
    });

    res.json(chartData);
  } catch (error) {
    console.error('Error fetching transaction time chart:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get revenue vs expenses chart data
router.get('/revenue-expenses-chart', async (req, res) => {
  try {
    const { user_id, branch_id, date_filter = 'month', user_type } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'user_id is required' });
    }

    // Get user role to determine access level
    const userRole = await getUserRole(user_id, user_type);
    if (!userRole) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Build date condition and format
    let dateCondition = '';
    let dateFormat = '';
    const today = new Date();

    switch (date_filter) {
      case 'week':
        const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        dateCondition = `AND DATE(date) >= '${weekAgo.toISOString().split('T')[0]}'`;
        dateFormat = 'DATE(date)';
        break;
      case 'month':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        dateCondition = `AND date >= '${monthStart.toISOString().split('T')[0]}'`;
        dateFormat = 'DATE(date)';
        break;
      case 'year':
        const yearStart = new Date(today.getFullYear(), 0, 1);
        dateCondition = `AND date >= '${yearStart.toISOString().split('T')[0]}'`;
        dateFormat = 'DATE_FORMAT(date, "%Y-%m")';
        break;
      default:
        dateCondition = '';
        dateFormat = 'DATE(date)';
    }

    // Build user condition for transactions
    let transactionUserCondition = '';
    if (branch_id) {
      transactionUserCondition = `(
        t.branch_id = ${branch_id} OR
        t.cashier_id IN (SELECT id FROM cashiers WHERE branch_id = ${branch_id}) OR
        (t.outlet_id = ${user_id} AND t.branch_id IS NULL)
      )`;
    } else {
      if (userRole === 'admin') {
        transactionUserCondition = `(
          t.outlet_id = ${user_id} OR
          t.branch_id IN (SELECT id FROM branches WHERE user_id = ${user_id}) OR
          t.cashier_id IN (SELECT id FROM cashiers WHERE user_id = ${user_id}) OR
          t.cashier_id IN (SELECT id FROM cashiers WHERE branch_id IN (SELECT id FROM branches WHERE user_id = ${user_id}))
        )`;
      } else if (userRole === 'branch') {
        transactionUserCondition = `(
          t.branch_id = ${user_id} OR
          t.cashier_id IN (SELECT id FROM cashiers WHERE branch_id = ${user_id})
        )`;
      } else {
        return res.status(403).json({ error: 'Access denied' });
      }
    }

    // Build user condition for expenses
    let expenseUserCondition = '';
    if (branch_id) {
      expenseUserCondition = `(
        e.branch_id = ${branch_id} OR
        e.user_id = ${user_id}
      )`;
    } else {
      if (userRole === 'admin') {
        expenseUserCondition = `(
          e.user_id = ${user_id} OR
          e.branch_id IN (SELECT id FROM branches WHERE user_id = ${user_id}) OR
          e.user_id IN (SELECT id FROM branches WHERE user_id = ${user_id})
        )`;
      } else if (userRole === 'branch') {
        expenseUserCondition = `e.branch_id = ${user_id}`;
      }
    }

    // Get revenue data
    const revenueQuery = `
      SELECT
        ${dateFormat} as date,
        SUM(t.total) as revenue
      FROM transactions t
      WHERE ${transactionUserCondition} ${dateCondition} AND t.payment_status = 'paid'
      GROUP BY ${dateFormat}
      ORDER BY date ASC
    `;

    // Get expenses data
    const expensesQuery = `
      SELECT
        ${dateFormat.replace('t.date', 'e.date')} as date,
        SUM(e.amount) as expenses
      FROM expenses e
      WHERE ${expenseUserCondition} ${dateCondition.replace('date', 'e.date')}
      GROUP BY ${dateFormat.replace('t.date', 'e.date')}
      ORDER BY date ASC
    `;

    const [revenueResults] = await pool.query(revenueQuery);
    const [expensesResults] = await pool.query(expensesQuery);

    // Combine data by date
    const dateMap = new Map();

    // Add revenue data
    revenueResults.forEach(row => {
      const dateKey = row.date;
      if (!dateMap.has(dateKey)) {
        dateMap.set(dateKey, { date: dateKey, revenue: 0, expenses: null });
      }
      dateMap.get(dateKey).revenue = parseFloat(row.revenue) || 0;
    });

    // Add expenses data
    expensesResults.forEach(row => {
      const dateKey = row.date;
      if (!dateMap.has(dateKey)) {
        dateMap.set(dateKey, { date: dateKey, revenue: 0, expenses: null });
      }
      dateMap.get(dateKey).expenses = parseFloat(row.expenses) || 0;
    });

    // Only include dates that have either revenue or expenses data
    // Remove entries where both revenue is 0 and expenses is null (no actual data)
    const filteredEntries = Array.from(dateMap.entries()).filter(([date, data]) => {
      return data.revenue > 0 || data.expenses !== null;
    });

    // Convert back to map for final processing
    const filteredDateMap = new Map(filteredEntries);

    // Convert to array and sort by date
    const chartData = Array.from(filteredDateMap.values()).sort((a, b) => {
      return new Date(a.date).getTime() - new Date(b.date).getTime();
    });

    // Add profit calculation and handle null expenses
    const finalData = chartData.map(item => ({
      ...item,
      expenses: item.expenses || 0, // Convert null to 0 for chart display
      profit: item.revenue - (item.expenses || 0)
    }));

    res.json(finalData);
  } catch (error) {
    console.error('Error fetching revenue vs expenses chart:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
